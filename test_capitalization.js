// Quick test of the capitalization function
const capitalizeFirstEnglishLetter = (name) => {
    if (!name || typeof name !== 'string') {
        return name || '';
    }

    // Find the first English letter (A-Z, a-z)
    const firstEnglishLetterMatch = name.match(/[a-zA-Z]/);
    
    if (!firstEnglishLetterMatch) {
        // No English letters found, return as is
        return name;
    }

    const firstEnglishLetterIndex = firstEnglishLetterMatch.index;
    const firstEnglishLetter = firstEnglishLetterMatch[0];
    
    // If the first English letter is already uppercase, return as is
    if (firstEnglishLetter === firstEnglishLetter.toUpperCase()) {
        return name;
    }

    // Capitalize the first English letter
    return name.substring(0, firstEnglishLetterIndex) + 
           firstEnglishLetter.toUpperCase() + 
           name.substring(firstEnglishLetterIndex + 1);
};

// Test cases
console.log('Testing capitalization function:');
console.log('john ->', capitalizeFirstEnglishLetter('john'));
console.log('mary ->', capitalizeFirstEnglishLetter('mary'));
console.log('John ->', capitalizeFirstEnglishLetter('John'));
console.log('testuser ->', capitalizeFirstEnglishLetter('testuser'));
console.log('admin ->', capitalizeFirstEnglishLetter('admin'));
console.log('user123 ->', capitalizeFirstEnglishLetter('user123'));
console.log('empty string ->', capitalizeFirstEnglishLetter(''));
console.log('null ->', capitalizeFirstEnglishLetter(null));
console.log('undefined ->', capitalizeFirstEnglishLetter(undefined));
