// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useCallback} from 'react';
import {View, Text, TouchableOpacity, ScrollView, StyleSheet, Animated} from 'react-native';

import CompassIcon from '@components/compass_icon';
import {useTheme} from '@context/theme';
import {changeOpacity, makeStyleSheetFromTheme} from '@utils/theme';

type SelectedEmoji = {
    id: string;
    character: string;
    name: string;
};

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 12,
            marginBottom: 8,
            paddingHorizontal: 12,
            paddingVertical: 10,
            minHeight: 44,
            borderWidth: 1,
            borderColor: changeOpacity(theme.centerChannelColor, 0.08),
        },
        scrollContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        emptyState: {
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            flex: 1,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.6),
            fontSize: 14,
            marginLeft: 8,
            fontStyle: 'italic',
        },
        emojiItem: {
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: changeOpacity(theme.buttonBg, 0.12),
            borderRadius: 20,
            paddingHorizontal: 10,
            paddingVertical: 6,
            marginRight: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.buttonBg, 0.25),
            shadowColor: theme.centerChannelColor,
            shadowOffset: {
                width: 0,
                height: 1,
            },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
        },
        emojiCharacter: {
            fontSize: 20,
            marginRight: 6,
        },
        removeButton: {
            padding: 4,
            borderRadius: 10,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
        },
    });
});

const EmojiPreview = ({selectedEmojis, onRemoveEmoji, testID = 'emoji_preview'}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);

    const handleRemoveEmoji = useCallback((id: string) => {
        onRemoveEmoji(id);
    }, [onRemoveEmoji]);

    if (selectedEmojis.length === 0) {
        return (
            <View style={styles.container} testID={`${testID}.container`}>
                <View style={styles.emptyState}>
                    <CompassIcon
                        name='emoticon-outline'
                        size={16}
                        color={changeOpacity(theme.centerChannelColor, 0.5)}
                    />
                    <Text style={styles.emptyText}>
                        Selected emojis will appear here
                    </Text>
                </View>
            </View>
        );
    }

    return (
        <View style={styles.container} testID={`${testID}.container`}>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.scrollContainer}
                testID={`${testID}.scroll_view`}
            >
                {selectedEmojis.map((emoji) => (
                    <View
                        key={emoji.id}
                        style={styles.emojiItem}
                        testID={`${testID}.emoji_item.${emoji.id}`}
                    >
                        <Text style={styles.emojiCharacter}>
                            {emoji.character}
                        </Text>
                        <TouchableOpacity
                            style={styles.removeButton}
                            onPress={() => handleRemoveEmoji(emoji.id)}
                            testID={`${testID}.remove_button.${emoji.id}`}
                            hitSlop={{top: 8, bottom: 8, left: 8, right: 8}}
                        >
                            <CompassIcon
                                name='close'
                                size={12}
                                style={styles.removeIcon}
                            />
                        </TouchableOpacity>
                    </View>
                ))}
            </ScrollView>
        </View>
    );
};

export default EmojiPreview;
export type {SelectedEmoji};
