// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useIntl } from "react-intl";
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ScrollView,
} from "react-native";

import Badge from "@components/badge";
import ChannelIcon from "@components/channel_icon";
import CompassIcon from "@components/compass_icon";
import { DateTime, General, Screens } from "@constants";
import { HOME_PADDING } from "@constants/view";
import { useTheme } from "@context/theme";
import { useIsTablet } from "@hooks/device";
import { isDMorGM } from "@utils/channel";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";
import { typography } from "@utils/typography";
import { getUserIdFromChannelName } from "@utils/user";

import { ChannelBody } from "./channel_body";

import type ChannelModel from "@typings/database/models/servers/channel";
import { useServerUrl } from "@app/context/server";
import type { PostModel } from "@app/database/models/server";
import { getMonthNumber } from "@app/constants/monthOfYear";
import { isYesterday } from "@app/utils/datetime";
import ArabicNumbers from "@app/utils/englishNumberToArabic";
import { fetchStatus } from "@actions/remote/user";
import { useDatabase } from "@nozbe/watermelondb/react";
import { queryUsersById } from "@queries/servers/user";
import { displayUsername } from "@utils/user";

import ProfilePicture from "@components/profile_picture";
import { useUserLocale } from "@context/user_locale";

import Svg, { Circle } from "react-native-svg";
import { goToScreen } from "@app/screens/navigation";
import type { FileModel } from "@app/database/models/server";

type Props = {
    channel: ChannelModel | Channel;
    currentUserId: string;
    hasDraft: boolean;
    isShownDraft?: boolean | undefined;
    isActive: boolean;
    isMuted: boolean;
    membersCount: number;
    isUnread: boolean;
    mentionsCount: number;
    onPress: (channel: ChannelModel | Channel) => void;
    teamDisplayName?: string;
    testID?: string;
    hasCall: boolean;
    isOnCenterBg?: boolean;
    showChannelName?: boolean;
    isOnHome?: boolean;
    lastPostDate?: number;
    lastPosts?: PostModel[] | undefined;
    isShowOnlyState?: boolean;
    hasFile?: FileModel[] | undefined;
};

export const ROW_HEIGHT = 65;
export const ROW_HEIGHT_WITH_TEAM = 68;

export const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        flexDirection: "row",
        alignItems: "center",
        borderBottomColor: changeOpacity(theme.centerChannelColor, 0.08),
        borderBottomWidth: 1,

        // backgroundColor:'green'
        //  paddingBottom: 7
    },
    icon: {
        marginRight: 12,
    },
    text: {
        color: theme.sidebarText, //color: changeOpacity(theme.sidebarText, 0.72),
    },
    highlight: {
        color: theme.sidebarUnreadText,
    },
    textOnCenterBg: {
        color: theme.centerChannelColor,
    },
    muted: {
        color: changeOpacity(theme.sidebarText, 0.32),
    },
    mutedOnCenterBg: {
        color: changeOpacity(theme.centerChannelColor, 0.8),
    },
    badge: {
        borderColor: theme.sidebarBg,
        //Overwrite default badge styles
        position: undefined,
        top: undefined,
        left: 24,
        alignSelf: undefined,
    },
    badgeOnCenterBg: {
        color: theme.buttonColor,
        backgroundColor: theme.buttonBg,
        borderColor: theme.centerChannelBg,
    },
    mutedBadge: {
        opacity: 0.32,
    },
    activeItem: {
        backgroundColor: changeOpacity(theme.sidebarTextActiveColor, 0.1),
        borderLeftColor: theme.sidebarTextActiveBorder,
        borderLeftWidth: 5,
    },
    textActive: {
        color: theme.sidebarText,
    },
    hasCall: {
        textAlign: "right",
    },
    filler: {
        flex: 1,
    },
}));

export const textStyle = StyleSheet.create({
    bold: typography("Heading", 100, "SemiBold"),
    regular: typography("Heading", 100),
});

const ChannelItem = ({
    channel,
    currentUserId,
    hasDraft,
    isActive,
    isMuted,
    membersCount,
    isUnread,
    mentionsCount,
    onPress,
    teamDisplayName = "",
    testID,
    hasCall,
    isOnCenterBg = false,
    showChannelName = false,
    isOnHome = false,
    lastPostDate = 0,
    lastPosts = undefined,
    isShownDraft = false,
    isShowOnlyState = false,
    hasFile = undefined,
}: Props) => {
    const { SECONDS } = DateTime;

    const intl = useIntl();

    const { formatMessage } = useIntl();
    const theme = useTheme();
    const isTablet = useIsTablet();
    const styles = getStyleSheet(theme);
    const serverUrl = useServerUrl();
    const database = useDatabase();
    const userLocale = useUserLocale();


    const [teamStatus, setTeamStatus] = useState<any[]>();
    const [usernames, setUsernames] = useState<Record<string, string>>({});
    const [userDisplayNames, setUserDisplayNames] = useState<Record<string, string>>({});
    const [watchedStatuses, setWatchedStatuses] = useState<Set<string>>(new Set());
    const [usersData, setUsersData] = useState<Record<string, any>>({});
    const channelName =
        showChannelName && !isDMorGM(channel) ? channel.name : "";

    // Make it bolded if it has unreads or mentions
    const isBolded = isUnread || mentionsCount > 0;
    const showActive = isActive && isTablet;

    const teammateId =
        channel.type === General.DM_CHANNEL
            ? getUserIdFromChannelName(currentUserId, channel.name)
            : undefined;
    const isOwnDirectMessage =
        channel.type === General.DM_CHANNEL && currentUserId === teammateId;

    let displayName =
        "displayName" in channel ? channel.displayName : channel.display_name;


    const userStatusContext = async () => {
        if (teammateId !== undefined) {
            try {
                // Fetch real status data from your custom plugin
                console.log(`🔄 Fetching real status data for user: ${teammateId}`);

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('fetchStatus timeout after 5 seconds')), 5000);
                });

                try {
                    // Try your custom plugin API
                    const data = await Promise.race([
                        fetchStatus(serverUrl, teammateId),
                        timeoutPromise
                    ]);

                    if (Array.isArray(data) && data.length > 0) {
                        console.log(`✅ Got custom plugin data:`, data.length, 'statuses');
                        setTeamStatus(data);
                        fetchUsernamesForStatuses(data);
                        return;
                    }
                } catch (pluginError) {
                    console.log(`⚠️ Custom plugin failed: ${pluginError}`);
                }

                // TEMPORARY: Use mock data when real data is not available
                // TODO: Remove this when real backend status data is ready
                console.log(`🔄 Using temporary mock status data for user: ${teammateId}`);
                const mockData = generateMockStatusData(teammateId);
                setTeamStatus(mockData);
                fetchUsernamesForStatuses(mockData);
                return; // Make sure we exit after setting mock data

            } catch (error) {
                console.log(`❌ Status fetch error: ${error}`);
                setTeamStatus(undefined);
            }
        }
    };

    // TEMPORARY: Mock status data generator - TO BE REMOVED when real backend data is ready
    const generateMockStatusData = (userId: string) => {
        const mockStatuses = [
            {
                user_id: userId,
                status: "online",
                is_watched: false,
                watched: false,
                created_at: Date.now() - 3600000, // 1 hour ago
                type: "text",
                text: "Working on new features! 🚀"
            },
            {
                user_id: userId,
                status: "away",
                is_watched: false,
                watched: false,
                created_at: Date.now() - 7200000, // 2 hours ago
                type: "image",
                image_url: "https://example.com/status-image.jpg",
                text: "Coffee break ☕"
            },
            {
                user_id: userId,
                status: "offline",
                is_watched: true,
                watched: true,
                created_at: Date.now() - 10800000, // 3 hours ago
                type: "text",
                text: "Meeting with the team 📝"
            }
        ];

        // Return 1-3 random statuses for variety
        const numStatuses = Math.floor(Math.random() * 3) + 1;
        return mockStatuses.slice(0, numStatuses);
    };

    const fetchUsernamesForStatuses = async (statuses: any[]) => {
        try {
            const userIds = statuses.map(status => status.user_id).filter(Boolean);
            if (userIds.length > 0) {
                const users = await queryUsersById(database, userIds).fetch();
                const usernameMap: Record<string, string> = {};
                const displayNameMap: Record<string, string> = {};
                const userDataMap: Record<string, any> = {};

                users.forEach(user => {
                    // Keep username for fallback
                    usernameMap[user.id] = displayUsername(user);

                    // Create display name from first_name and last_name
                    const firstName = user.firstName || '';
                    const lastName = user.lastName || '';

                    if (firstName && lastName) {
                        displayNameMap[user.id] = `${firstName} ${lastName}`;
                    } else if (firstName) {
                        displayNameMap[user.id] = firstName;
                    } else if (lastName) {
                        displayNameMap[user.id] = lastName;
                    } else {
                        // Fallback to username if no first/last name available
                        displayNameMap[user.id] = displayUsername(user);
                    }

                    userDataMap[user.id] = user;
                });

                setUsernames(usernameMap);
                setUserDisplayNames(displayNameMap);
                setUsersData(userDataMap);
            }
        } catch (error) {
            console.log(`\n\nError fetching usernames: ${error}\n\n`);
        }
    };



    useEffect(() => {
        if (isShowOnlyState && teammateId) {
            userStatusContext();
        }
    }, [teammateId, isShowOnlyState, serverUrl]);

    const windowWidth = Dimensions.get("window").width;

    if (isOwnDirectMessage) {
        displayName = formatMessage(
            {
                id: "channel_header.directchannel.you",
                defaultMessage: "{displayName} (you)",
            },
            { displayName }
        );
    }

    const deleteAt =
        "deleteAt" in channel ? channel.deleteAt : channel.delete_at;
    const channelItemTestId = `${testID}.${channel.name}`;

    const height = useMemo(() => {
        return teamDisplayName && !isTablet ? ROW_HEIGHT_WITH_TEAM : ROW_HEIGHT;
    }, [teamDisplayName, isTablet]);

    const handleOnPress = useCallback(() => {
        console.log(`\n\nthis the channel id  ${channel.id}n\n`);
        // setFirstTime(false)
        onPress(channel);
    }, [channel.id]);

    const textStyles = useMemo(
        () => [
            isBolded && !isMuted ? textStyle.bold : textStyle.regular,
            styles.text,
            isBolded && styles.highlight,
            showActive ? styles.textActive : null,
            isOnCenterBg ? styles.textOnCenterBg : null,
            isMuted && styles.muted,
            styles.mutedOnCenterBg,
        ],
        [isBolded, styles, isMuted, showActive, isOnCenterBg]
    );

    const containerStyle = useMemo(
        () => [
            styles.container,
            isOnHome && HOME_PADDING,
            showActive && styles.activeItem,
            showActive &&
                isOnHome && {
                    paddingLeft:
                        HOME_PADDING.paddingLeft -
                        styles.activeItem.borderLeftWidth,
                },
            { minHeight: height },
        ],
        [height, showActive, styles, isOnHome]
    );

    const fullDate = (date?: Date | null) => {
        if (date === null) return "";
        const difference = (Date.now() - date!.getTime()) / 1000;

        if (difference > SECONDS.DAYS_10Years) return "";

        const dateToList = date!.toUTCString().split(" ");

        let dateHOlder = "";
        switch (difference < SECONDS.DAY) {
            case true:
                {
                    const timHolder = dateToList[4].split(":");
                    const hour = (Number(timHolder[0]) + 3) % 24;
                    const displayHour = hour % 12 === 0 ? 12 : hour % 12;
                    dateHOlder += ArabicNumbers(displayHour);
                    dateHOlder += ":" + ArabicNumbers(Number(timHolder[1]));
                    dateHOlder += `${hour >= 12 ? " م" : " ص"}`;
                }
                break;
            default: {
                switch (isYesterday(date!)) {
                    case true:
                        {
                            dateHOlder = intl.formatMessage({
                                id: "friendly_date.yesterday",
                                defaultMessage: "Yesterday",
                            });
                        }
                        break;
                    default: {
                        // dateHOlder += ArabicNumbers(Number(dateToList[1])) + '/' + ArabicNumbers(getMonthNumber(dateToList[2])) + '/' + ArabicNumbers(Number(dateToList[3]))

                        dateHOlder +=
                            ArabicNumbers(Number(dateToList[3])) + "/";
                        dateHOlder +=
                            ArabicNumbers(getMonthNumber(dateToList[2])) + "/";
                        dateHOlder += ArabicNumbers(Number(dateToList[1]));
                    }
                }
            }
        }

        //const fullDateHOlder = (date.getDay() > 0 ? date.getDate() : 1) + '/' + date.getMonth() + '/' + date.getFullYear()
        return dateHOlder;
    };

    const statusNavigation = () => {
        console.log(`🔥 ChannelIcon with status overlay tapped!`);
        console.log(`\n\nthis the team status ${teamStatus}\n\n`);

        if (teamStatus !== undefined && teamStatus.length > 0) {
            // Use the first status for navigation (consistent with handleStatusClick pattern)
            const firstStatus = teamStatus[0];
            const statusKey = `${firstStatus.user_id}_${firstStatus.created_at || 0}`;
            const isWatched = firstStatus.is_watched || firstStatus.watched || watchedStatuses.has(statusKey);
            const displayName = userDisplayNames[firstStatus.user_id] || usernames[firstStatus.user_id] || "User";

            console.log(`🚀 Navigating to UserStatusScreen via ChannelIcon for user: ${firstStatus.user_id} (${displayName})`);

            // Mark as watched
            if (!isWatched) {
                setWatchedStatuses((prev: Set<string>) => new Set([...prev, statusKey]));
            }

            // Navigate to specific user's status screen (using first user's statuses)
            const userStatuses = teamStatus.filter(s => s.user_id === firstStatus.user_id);
            if (userStatuses.length > 0) {
                console.log(`✅ Calling goToScreen for USER_STATUS_SCREEN via ChannelIcon`);
                goToScreen(
                    Screens.USER_STATUS_SCREEN,
                    "Status",
                    {
                        customStatus: userStatuses,
                        channelName: channel.name,
                        channelText: displayName,
                        currentUserId: currentUserId,
                        selectedUserId: firstStatus.user_id, // Pass the specific user ID
                        selectedUserName: displayName, // Pass the display name
                        teamStatus: teamStatus, // Pass complete team status for sequential navigation
                        watchedStatuses: watchedStatuses, // Pass watched statuses tracking
                        setWatchedStatuses: setWatchedStatuses, // Pass watched statuses setter
                    },
                    {
                        topBar: {
                            visible: false,
                            backButton: {
                                visible: true,
                                color: theme.sidebarHeaderTextColor,
                            },
                            background: {
                                color: "#00987e",
                            },
                            elevation: 0,
                        },
                        layout: {
                            componentBackgroundColor: theme.centerChannelBg,
                        },
                        statusBar: {
                            visible: true,
                            backgroundColor: "#00987e",
                            style: "light",
                        },
                    }
                );
            }
        }
    };

    const userStatus = () => {
        return (
            <ChannelBody
                isShowOnlyState={isShowOnlyState}
                displayName={displayName}
                isMuted={isMuted}
                teamDisplayName={teamDisplayName}
                teammateId={teammateId}
                testId={channelItemTestId}
                textStyles={textStyles}
                channelName={channel.name}
                // lastMessage={lastPosts === undefined ? undefined : lastPosts[0]?.message || undefined}
                lastMessage={lastPosts === undefined ? undefined : lastPosts[0]}
                hasFile={hasFile !== undefined && hasFile.length > 0}
            />
        );
    };

    if (isShowOnlyState && teamStatus !== undefined && teamStatus.length > 0) {
        return (
            <View
                style={{
                    marginStart: 2,
                    paddingRight: 0,
                    paddingLeft: 0,
                    paddingBottom: -10, // Increased bottom padding to prevent overlap with tab bar
                    paddingTop: -10, // Added top padding for better spacing
                    minHeight: 80, // Increased height to accommodate larger avatars + username + spacing
                }}
            >
                <ScrollView
                    horizontal={true}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{
                        flexDirection: userLocale.startsWith('ar') ? 'row-reverse' : 'row', // RTL support
                        alignItems: 'flex-start', // Changed to flex-start to accommodate usernames
                        paddingHorizontal: 0,
                        paddingRight: 5, // Add extra padding to ensure last item is fully visible
                    }}
                    style={{
                        flexGrow: 1, // Allow horizontal scrolling
                        flex: 1, // Take available space
                    }}
                    scrollEnabled={true} // Explicitly enable scrolling
                    bounces={true} // Enable bounce effect for better UX
                >
                    {(() => {
                        // Group statuses by user_id to avoid duplicate avatars
                        const groupedStatuses = teamStatus.reduce((acc: Record<string, any[]>, status: any) => {
                            if (!status || !status.user_id) {
                                return acc;
                            }
                            if (!acc[status.user_id]) {
                                acc[status.user_id] = [];
                            }
                            acc[status.user_id].push(status);
                            return acc;
                        }, {});

                        // Convert grouped statuses to array for rendering
                        const groupedStatusArray = Object.entries(groupedStatuses).map(([userId, userStatuses]: [string, any[]]) => ({
                            userId,
                            statuses: userStatuses
                        }));

                        return groupedStatusArray.map((groupedStatus, index) => {
                            const { userId, statuses } = groupedStatus;

                            // Determine if any status is unviewed to show correct visual state
                            const hasUnviewedStatus = statuses.some(status => {
                                const statusKey = `${status.user_id}_${status.created_at || 0}`;
                                const isWatched = status.is_watched || status.watched || watchedStatuses.has(statusKey);
                                return !isWatched;
                            });
                            const circleColor = hasUnviewedStatus ? theme.buttonBg : "#E9E9E9";

                            // Get display name (first_name last_name) for this user, fallback to username
                            const displayName = userDisplayNames[userId] || usernames[userId] || "User";

                            // Handle status click to navigate to user's status screen
                            const handleStatusClick = () => {
                                console.log(`🔥 Status circle tapped! User ID: ${userId}, Display Name: ${displayName}, Statuses: ${statuses.length}`);

                                // Mark all user's statuses as watched
                                const statusKeys = statuses.map(status => `${status.user_id}_${status.created_at || 0}`);
                                setWatchedStatuses((prev: Set<string>) => {
                                    const newSet = new Set(prev);
                                    statusKeys.forEach(key => newSet.add(key));
                                    return newSet;
                                });

                                console.log(`🚀 Navigating to UserStatusScreen with ${statuses.length} statuses for user ${userId}`);

                                if (statuses.length > 0) {
                                    console.log(`✅ Calling goToScreen for USER_STATUS_SCREEN via grouped status circle`);
                                    goToScreen(
                                        Screens.USER_STATUS_SCREEN,
                                        "Status",
                                        {
                                            customStatus: statuses,
                                            channelName: channel.name,
                                            channelText: displayName,
                                            currentUserId: currentUserId,
                                            selectedUserId: userId, // Pass the specific user ID
                                            selectedUserName: displayName, // Pass the display name
                                            teamStatus: teamStatus, // Pass complete team status for sequential navigation
                                            watchedStatuses: watchedStatuses, // Pass watched statuses tracking
                                            setWatchedStatuses: setWatchedStatuses, // Pass watched statuses setter
                                        },
                                        {
                                            topBar: {
                                                visible: false,
                                                backButton: {
                                                    visible: true,
                                                    color: theme.sidebarHeaderTextColor,
                                                },
                                                background: {
                                                    color: "#00987e",
                                                },
                                                elevation: 0,
                                            },
                                            layout: {
                                                componentBackgroundColor: theme.centerChannelBg,
                                            },
                                            statusBar: {
                                                visible: true,
                                                backgroundColor: "#00987e",
                                                style: "light",
                                            },
                                        }
                                    );
                                }
                            };

                    // Determine if Arabic locale for RTL support
                    const isArabic = userLocale.startsWith('ar');

                    return (
                        <TouchableOpacity
                            key={`${channel.id}_${userId}_grouped_${index}`}
                            onPress={handleStatusClick}
                            activeOpacity={0.7}
                            style={{
                                position: "relative",
                                height: 100, // Increased height for larger avatar + username + spacing
                                width: 60, // Wider to accommodate larger avatar and text
                                marginEnd: 10,
                                marginRight: index < groupedStatusArray.length - 1 ? 12 : 0, // Adjusted for grouped array
                                alignItems: 'center',
                                justifyContent: 'flex-start',
                                backgroundColor: 'transparent', // Ensure transparent background
                            }}
                        >
                            {/* SVG circle with segments representing multiple statuses */}
                            <Svg
                                style={{
                                    position: 'absolute',
                                    top: 2, // Slight offset from top
                                    left: 2, // Centered positioning for 60px container
                                    zIndex: 10,
                                }}
                                width={56} // Larger to accommodate 48px avatar
                                height={56}
                            >
                                {statuses.length === 1 ? (
                                    // Single status - show full circle
                                    <Circle
                                        cx="28" // Center X (half of 56px width)
                                        cy="28" // Center Y (half of 56px height)
                                        r={26} // Radius for 48px avatar (48/2 + 1)
                                        stroke={circleColor} // Dynamic color based on watched status
                                        strokeWidth={2.5} // Thicker for better visibility
                                        fill="none" // No fill
                                    />
                                ) : (
                                    // Multiple statuses - show segmented circle
                                    Array.from({ length: statuses.length }).map((_, segmentIndex) => {
                                        const radius = 26; // Radius of the circle
                                        const strokeWidth = 2; // Stroke width
                                        const circumference = 2 * Math.PI * radius; // Total circumference of the circle

                                        // Conditional logic for gap size
                                        const gapSize = statuses.length > 1 ? 2 : 0; // Gap between segments
                                        const visibleLength = circumference / statuses.length - gapSize; // Adjust visible length
                                        const dashArray = statuses.length > 1
                                            ? `${visibleLength} ${gapSize}`
                                            : `${circumference} 0`; // Full circle for a single segment

                                        // Calculate rotation for each segment
                                        const rotationAngle = (360 / statuses.length) * segmentIndex;
                                        const strokeDashoffset = -segmentIndex * (circumference / statuses.length);

                                        // Calculate individual segment color based on this specific status's watched state
                                        const currentStatus = statuses[segmentIndex];
                                        const statusKey = `${currentStatus.user_id}_${currentStatus.created_at || 0}`;
                                        const isWatched = currentStatus.is_watched || currentStatus.watched || watchedStatuses.has(statusKey);
                                        const segmentColor = isWatched ? "#E9E9E9" : theme.buttonBg; // #E9E9E9 for viewed, theme.buttonBg (#00987E) for unviewed

                                        return (
                                            <Circle
                                                key={segmentIndex}
                                                cx="28"
                                                cy="28"
                                                r={radius}
                                                stroke={segmentColor}
                                                strokeWidth={strokeWidth}
                                                fill="none"
                                                strokeDasharray={dashArray}
                                                strokeDashoffset={strokeDashoffset}
                                                transform={`rotate(${rotationAngle} 28 28)`}
                                            />
                                        );
                                    })
                                )}
                            </Svg>

                            {/* Real user profile picture - larger size */}
                            <ProfilePicture
                                author={usersData[userId]}
                                size={48} // Increased from 42px to 48px
                                showStatus={false}
                                containerStyle={{
                                    marginTop: 6, // Adjusted for proper positioning
                                    zIndex: 15, // Ensure avatar appears above circle
                                }}
                            />

                            {/* Display name (first_name last_name) text with Arabic/RTL support - improved visibility */}
                            <Text
                                style={{
                                    fontSize: 13, // Larger for better readability
                                    
                                    color: theme.centerChannelColor,
                                    marginTop: 8, // Increased spacing from avatar
                                    marginBottom: 5, // Reduced to prevent overlap with tab bar
                                    textAlign: 'center',
                                    width: 58, // Fixed width to match container
                                    fontFamily: isArabic ? 'IBMPlexSansArabic-Regular' : 'System',
                                    writingDirection: isArabic ? 'rtl' : 'ltr',
                                    fontWeight: '600', // Bolder for better visibility
                                    backgroundColor: 'transparent', // Ensure no background interference
                                    zIndex: 20, // Ensure text appears above other elements
                                }}
                                numberOfLines={2}
                                ellipsizeMode="tail"
                            >
                                {displayName}
                            </Text>
                        </TouchableOpacity>
                    );
                });
                    })()}
                </ScrollView>
            </View>
        );
    } else if (
        isShowOnlyState &&
        (teamStatus === undefined || teamStatus?.length === 0)
    )
        return null;
    //case for draft page
    if (isShownDraft && hasDraft) {
        return (
            <View style={containerStyle} testID={channelItemTestId}>
                <View style={{ position: "relative" }}>
                    {teamStatus && (
                        <Svg
                            style={{
                                top: -44,
                                start: -49.2,
                                position: "absolute",
                            }}
                            width={120}
                            height={120}
                        >
                            {Array.from({ length: teamStatus.length }).map(
                                (_, index) => {
                                    const radius = 20; // Radius of the circle
                                    const strokeWidth = 1.4; // Stroke width
                                    const circumference = 2 * Math.PI * radius; // Total circumference of the circle

                                    // Conditional logic for gap size
                                    const gapSize =
                                        teamStatus.length > 1 ? 2 : 0; // No gap for a single segment
                                    const visibleLength =
                                        circumference / teamStatus.length -
                                        gapSize; // Adjust visible length
                                    const dashArray =
                                        teamStatus.length > 1
                                            ? `${visibleLength} ${gapSize}`
                                            : `${circumference} 0`; // Full circle for a single segment

                                    return (
                                        <Circle
                                            key={index}
                                            cx="60" // Center X
                                            cy="60" // Center Y
                                            r={radius} // Circle radius
                                            stroke={theme.buttonBg} // Segment color
                                            strokeWidth={strokeWidth} // Thickness of the circle segments
                                            fill="none" // No fill
                                            strokeDasharray={dashArray} // Stroke pattern
                                            strokeDashoffset={
                                                -index *
                                                (visibleLength + gapSize)
                                            } // Offset for positioning
                                        />
                                    );
                                }
                            )}
                        </Svg>
                    )}

                    <ChannelIcon
                        hasDraft={hasDraft}
                        isActive={isTablet && isActive}
                        isOnCenterBg={isOnCenterBg}
                        isUnread={isBolded}
                        isArchived={deleteAt > 0}
                        membersCount={membersCount}
                        name={channel.name}
                        shared={channel.shared}
                        size={32}
                        type={channel.type}
                        isMuted={isMuted}
                        style={styles.icon}
                    />
                </View>
                <TouchableOpacity
                    onPress={handleOnPress}
                    style={{
                        flexDirection: "row",
                        marginEnd: 30,
                    }}
                >
                    <View
                        style={{
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "flex-start",
                            justifyContent: "center",
                            marginEnd: 4,
                        }}
                    >
                        <ChannelBody
                            displayName={displayName}
                            isMuted={isMuted}
                            teamDisplayName={teamDisplayName}
                            teammateId={teammateId}
                            testId={channelItemTestId}
                            textStyles={textStyles}
                            channelName={channelName}
                            lastMessage={lastPosts === undefined ? undefined : lastPosts[0]}
                        />
                    </View>

                    <View
                        style={{
                            flex: 1,
                        }}
                    />
                    <View
                        style={{
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                            flex: 1,
                        }}
                    >
                        {lastPosts !== undefined && (
                            <View
                                style={{
                                    //marginStart: 15
                                    maxWidth: 90,
                                    
                                }}
                            >
                                <Text
                                    style={{
                                        fontFamily: "IBMPlexSansArabic-Regular",

                                        color: changeOpacity(
                                            theme.sidebarText,
                                            0.4
                                        ),
                                    }}
                                >
                                    {fullDate(new Date(lastPostDate))}
                                </Text>
                            </View>
                        )}

                        <View
                            style={{
                                flexDirection: "row",
                                justifyContent: "space-between",
                            }}
                        >
                            <View>
                                <Badge
                                    visible={mentionsCount > 0}
                                    value={mentionsCount}
                                    style={[
                                        styles.badge,
                                        isMuted && styles.mutedBadge,
                                        isOnCenterBg && styles.badgeOnCenterBg,
                                    ]}
                                />
                            </View>

                            {hasCall && (
                                <CompassIcon
                                    name="phone-in-talk"
                                    size={16}
                                    style={[textStyles, { bottom: 5 }]}
                                />
                            )}
                        </View>
                    </View>
                </TouchableOpacity>
            </View>
        );
    } else if (isShownDraft && !hasDraft) return null;

    return (
        <View
            style={[
                !isShowOnlyState && containerStyle,
                !isShowOnlyState && {
                    paddingVertical: "auto",
                },
            ]}
            testID={channelItemTestId}
        >
            {isShowOnlyState ? (
                userStatus()
            ) : (
                <>
                    <TouchableOpacity
                        onPress={statusNavigation}
                        style={{ position: "relative", marginEnd: 5 }}
                    >
                        {teamStatus && (
                            <Svg
                                style={{
                                    top: -44,
                                    start: -44.2,
                                    position: "absolute",
                                }}
                                width={120}
                                height={120}
                            >
                                {Array.from({ length: teamStatus.length }).map(
                                    (_, index) => {
                                        const radius = 19; // Radius of the circle
                                        const strokeWidth = 1.4; // Stroke width
                                        const circumference =
                                            2 * Math.PI * radius; // Total circumference of the circle

                                        // Conditional logic for gap size
                                        const gapSize =
                                            teamStatus.length > 1 ? 2 : 0; // No gap for a single segment
                                        const visibleLength =
                                            circumference / teamStatus.length -
                                            gapSize; // Adjust visible length
                                        const dashArray =
                                            teamStatus.length > 1
                                                ? `${visibleLength} ${gapSize}`
                                                : `${circumference} 0`; // Full circle for a single segment

                                        return (
                                            <Circle
                                                key={index}
                                                cx="60" // Center X
                                                cy="60" // Center Y
                                                r={radius} // Circle radius
                                                stroke={theme.buttonBg} // Segment color
                                                strokeWidth={strokeWidth} // Thickness of the circle segments
                                                fill="none" // No fill
                                                strokeDasharray={dashArray} // Stroke pattern
                                                strokeDashoffset={
                                                    -index *
                                                    (visibleLength + gapSize)
                                                } // Offset for positioning
                                            />
                                        );
                                    }
                                )}
                            </Svg>
                        )}

                        <ChannelIcon
                            draftIconSize={27}
                            hasDraft={hasDraft}
                            isActive={isTablet && isActive}
                            isOnCenterBg={isOnCenterBg}
                            isUnread={isBolded}
                            isArchived={deleteAt > 0}
                            membersCount={membersCount}
                            name={channel.name}
                            shared={channel.shared}
                            size={32}
                            type={channel.type}
                            isMuted={isMuted}
                            style={[styles.icon, { 
                                    borderWidth: 1.5,
                                    borderRadius: 20,
                                    padding: 8,
                                    height: 40,
                                    width: 40,
                                    borderColor: theme.buttonBg, }]}
                        />
                    </TouchableOpacity>

                    <TouchableOpacity
                        onPress={handleOnPress}
                        style={{
                            flexDirection: "row",
                            width:
                                windowWidth -
                                (hasCall ? (mentionsCount > 0 ? 80 : 90) : 90),
                            marginStart: 0,
                            //backgroundColor:'green'
                        }}
                    >
                        <View
                            style={{
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "flex-start",
                                justifyContent: "center",
                                width: windowWidth - 175,
                                // backgroundColor:'red',
                                overflow: "hidden",
                            }}
                        >
                            {userStatus()}
                        </View>

                        <View
                            style={{
                                //marginStart:5,
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "center",
                                flex: 1,
                            }}
                        >
                            {lastPosts !== undefined && (
                                <View style={{
                                    maxWidth: 100,
                                    marginStart: 5,
                                    position: 'absolute',
                                    right: 3,
                                    top: 0,
                                }}>
                                    <Text
                                        style={{
                                            fontFamily:
                                                "IBMPlexSansArabic-Regular",
                                            color: changeOpacity(
                                                theme.sidebarText,
                                                0.4
                                            ),
                                        }}
                                    >
                                        {fullDate(new Date(lastPostDate))}
                                    </Text>
                                </View>
                            )}

                            <View
                                style={{
                                    flexDirection: "row",
                                    justifyContent: "space-between",
                                }}
                            >
                                <View>
                                    <Badge
                                        visible={mentionsCount > 0}
                                        value={mentionsCount}
                                        style={[
                                            styles.badge,
                                            isMuted && styles.mutedBadge,
                                            styles.badgeOnCenterBg,
                                        ]}
                                    />
                                </View>

                                {hasCall && (
                                    <CompassIcon
                                        name="phone-in-talk"
                                        size={16}
                                        style={[textStyles, { bottom: 5 }]}
                                    />
                                )}
                            </View>
                        </View>
                    </TouchableOpacity>
                </>
            )}
        </View>
    );
};

export default ChannelItem;
