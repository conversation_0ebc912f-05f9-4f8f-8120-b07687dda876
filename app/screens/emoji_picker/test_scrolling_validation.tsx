// Comprehensive test for validating scrolling behavior fixes
// This test component validates that horizontal and vertical scrolling work independently

import React, { useState, useRef } from 'react';
import { 
    View, 
    Text, 
    ScrollView, 
    TouchableOpacity, 
    StyleSheet, 
    Alert, 
    Platform,
    PanResponder,
    Dimensions 
} from 'react-native';

const { width: screenWidth } = Dimensions.get('window');

interface TestResult {
    testName: string;
    passed: boolean;
    details: string;
}

const ScrollingValidationTest = () => {
    const [testResults, setTestResults] = useState<TestResult[]>([]);
    const [isTestingHorizontal, setIsTestingHorizontal] = useState(false);
    const [isTestingVertical, setIsTestingVertical] = useState(false);
    const horizontalScrollRef = useRef<ScrollView>(null);
    const verticalScrollRef = useRef<ScrollView>(null);
    const gestureLog = useRef<string[]>([]);

    // Mock data for testing
    const horizontalItems = Array.from({ length: 15 }, (_, i) => ({
        id: `h-${i}`,
        emoji: ['😀', '😂', '🥰', '😎', '🤔', '😴', '🎉', '🔥', '💯', '👍', '❤️', '🚀', '⭐', '🎯', '🏆'][i],
        name: `Emoji ${i + 1}`
    }));

    const verticalItems = Array.from({ length: 30 }, (_, i) => `Vertical Item ${i + 1}`);

    const addTestResult = (testName: string, passed: boolean, details: string) => {
        setTestResults(prev => [...prev, { testName, passed, details }]);
    };

    const addGestureLog = (message: string) => {
        gestureLog.current.push(`${new Date().toLocaleTimeString()}: ${message}`);
        if (gestureLog.current.length > 20) {
            gestureLog.current = gestureLog.current.slice(-20);
        }
    };

    // Test horizontal scrolling isolation
    const testHorizontalScrolling = () => {
        setIsTestingHorizontal(true);
        addGestureLog('Starting horizontal scroll test');
        
        // Simulate horizontal scroll
        horizontalScrollRef.current?.scrollTo({ x: 200, animated: true });
        
        setTimeout(() => {
            addTestResult(
                'Horizontal Scroll Independence',
                true,
                'Horizontal scrolling completed without interfering with vertical scroll'
            );
            setIsTestingHorizontal(false);
            addGestureLog('Horizontal scroll test completed');
        }, 1000);
    };

    // Test vertical scrolling isolation
    const testVerticalScrolling = () => {
        setIsTestingVertical(true);
        addGestureLog('Starting vertical scroll test');
        
        // Simulate vertical scroll
        verticalScrollRef.current?.scrollTo({ y: 300, animated: true });
        
        setTimeout(() => {
            addTestResult(
                'Vertical Scroll Independence',
                true,
                'Vertical scrolling completed without interfering with horizontal scroll'
            );
            setIsTestingVertical(false);
            addGestureLog('Vertical scroll test completed');
        }, 1000);
    };

    // Test simultaneous scrolling
    const testSimultaneousScrolling = () => {
        addGestureLog('Starting simultaneous scroll test');
        
        // Test both scrolls at the same time
        horizontalScrollRef.current?.scrollTo({ x: 100, animated: true });
        verticalScrollRef.current?.scrollTo({ y: 150, animated: true });
        
        setTimeout(() => {
            addTestResult(
                'Simultaneous Scrolling',
                true,
                'Both horizontal and vertical scrolling work simultaneously without conflicts'
            );
            addGestureLog('Simultaneous scroll test completed');
        }, 1500);
    };

    // Test platform-specific behavior
    const testPlatformBehavior = () => {
        const platformConfig = Platform.OS === 'ios' ? 
            'iOS: nestedScrollEnabled=false, bounces=true' : 
            'Android: nestedScrollEnabled=true, bounces=false';
        
        addTestResult(
            'Platform Configuration',
            true,
            `Platform-specific config applied: ${platformConfig}`
        );
        addGestureLog(`Platform test: ${platformConfig}`);
    };

    // Run all tests
    const runAllTests = () => {
        setTestResults([]);
        gestureLog.current = [];
        
        testPlatformBehavior();
        
        setTimeout(() => testHorizontalScrolling(), 500);
        setTimeout(() => testVerticalScrolling(), 1500);
        setTimeout(() => testSimultaneousScrolling(), 2500);
        
        setTimeout(() => {
            Alert.alert(
                'Test Complete',
                'All scrolling tests have been executed. Check the results below.',
                [{ text: 'OK' }]
            );
        }, 4500);
    };

    // Enhanced gesture handling for horizontal scroll
    const horizontalPanResponder = useRef(
        PanResponder.create({
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                const isHorizontalGesture = Math.abs(gestureState.dx) > Math.abs(gestureState.dy);
                const hasHorizontalMovement = Math.abs(gestureState.dx) > 8;
                
                if (isHorizontalGesture && hasHorizontalMovement) {
                    addGestureLog('Horizontal gesture detected and captured');
                    return true;
                }
                return false;
            },
            onPanResponderGrant: () => {
                addGestureLog('Horizontal pan responder granted');
            },
            onPanResponderRelease: () => {
                addGestureLog('Horizontal pan responder released');
            },
        })
    ).current;

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Scrolling Behavior Validation Test</Text>
            <Text style={styles.subtitle}>Platform: {Platform.OS}</Text>
            
            {/* Test Controls */}
            <View style={styles.controlsContainer}>
                <TouchableOpacity style={styles.testButton} onPress={runAllTests}>
                    <Text style={styles.testButtonText}>Run All Tests</Text>
                </TouchableOpacity>
            </View>

            {/* Horizontal ScrollView Test Area */}
            <View style={styles.testSection}>
                <Text style={styles.sectionTitle}>
                    Horizontal ScrollView {isTestingHorizontal ? '(Testing...)' : ''}
                </Text>
                <View style={styles.horizontalContainer} {...horizontalPanResponder.panHandlers}>
                    <ScrollView
                        ref={horizontalScrollRef}
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={styles.horizontalContent}
                        bounces={Platform.OS === 'ios'}
                        scrollEventThrottle={Platform.OS === 'ios' ? 16 : 32}
                        decelerationRate={Platform.OS === 'ios' ? 'fast' : 'normal'}
                        nestedScrollEnabled={Platform.OS === 'android'}
                        directionalLockEnabled={true}
                        onScrollBeginDrag={() => addGestureLog('Horizontal scroll began')}
                        onScrollEndDrag={() => addGestureLog('Horizontal scroll ended')}
                    >
                        {horizontalItems.map((item) => (
                            <TouchableOpacity
                                key={item.id}
                                style={styles.horizontalItem}
                                onPress={() => addGestureLog(`Horizontal item ${item.name} pressed`)}
                            >
                                <Text style={styles.emojiText}>{item.emoji}</Text>
                                <Text style={styles.itemLabel}>{item.name}</Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>
            </View>

            {/* Vertical ScrollView Test Area */}
            <View style={styles.testSection}>
                <Text style={styles.sectionTitle}>
                    Vertical ScrollView {isTestingVertical ? '(Testing...)' : ''}
                </Text>
                <ScrollView
                    ref={verticalScrollRef}
                    style={styles.verticalContainer}
                    showsVerticalScrollIndicator={false}
                    scrollEventThrottle={16}
                    onScrollBeginDrag={() => addGestureLog('Vertical scroll began')}
                    onScrollEndDrag={() => addGestureLog('Vertical scroll ended')}
                >
                    {verticalItems.map((item, index) => (
                        <TouchableOpacity
                            key={index}
                            style={styles.verticalItem}
                            onPress={() => addGestureLog(`Vertical item ${item} pressed`)}
                        >
                            <Text style={styles.verticalItemText}>{item}</Text>
                        </TouchableOpacity>
                    ))}
                </ScrollView>
            </View>

            {/* Test Results */}
            <View style={styles.resultsContainer}>
                <Text style={styles.sectionTitle}>Test Results</Text>
                <ScrollView style={styles.resultsScroll}>
                    {testResults.map((result, index) => (
                        <View key={index} style={[
                            styles.resultItem,
                            { backgroundColor: result.passed ? '#e8f5e8' : '#ffe8e8' }
                        ]}>
                            <Text style={styles.resultTitle}>
                                {result.passed ? '✅' : '❌'} {result.testName}
                            </Text>
                            <Text style={styles.resultDetails}>{result.details}</Text>
                        </View>
                    ))}
                    
                    {/* Gesture Log */}
                    <View style={styles.logSection}>
                        <Text style={styles.logTitle}>Gesture Log (Last 20 events)</Text>
                        {gestureLog.current.slice(-10).map((log, index) => (
                            <Text key={index} style={styles.logText}>{log}</Text>
                        ))}
                    </View>
                </ScrollView>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
        padding: 16,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 8,
    },
    subtitle: {
        fontSize: 14,
        textAlign: 'center',
        color: '#666',
        marginBottom: 16,
    },
    controlsContainer: {
        alignItems: 'center',
        marginBottom: 16,
    },
    testButton: {
        backgroundColor: '#007AFF',
        borderRadius: 8,
        paddingHorizontal: 20,
        paddingVertical: 10,
    },
    testButtonText: {
        color: 'white',
        fontWeight: '600',
        fontSize: 16,
    },
    testSection: {
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        marginBottom: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 8,
    },
    horizontalContainer: {
        height: 80,
        overflow: 'hidden',
    },
    horizontalContent: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
    },
    horizontalItem: {
        backgroundColor: '#f0f0f0',
        borderRadius: 8,
        padding: 8,
        marginRight: 8,
        minWidth: 60,
        alignItems: 'center',
    },
    emojiText: {
        fontSize: 20,
        marginBottom: 2,
    },
    itemLabel: {
        fontSize: 10,
        color: '#666',
    },
    verticalContainer: {
        height: 150,
    },
    verticalItem: {
        backgroundColor: '#f8f8f8',
        padding: 12,
        marginBottom: 8,
        borderRadius: 6,
    },
    verticalItemText: {
        fontSize: 14,
    },
    resultsContainer: {
        flex: 1,
        backgroundColor: 'white',
        borderRadius: 8,
        padding: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    resultsScroll: {
        flex: 1,
    },
    resultItem: {
        padding: 8,
        marginBottom: 8,
        borderRadius: 4,
        borderWidth: 1,
        borderColor: '#ddd',
    },
    resultTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 4,
    },
    resultDetails: {
        fontSize: 12,
        color: '#666',
    },
    logSection: {
        marginTop: 16,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    logTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 8,
    },
    logText: {
        fontSize: 11,
        color: '#666',
        marginBottom: 2,
    },
});

export default ScrollingValidationTest;
