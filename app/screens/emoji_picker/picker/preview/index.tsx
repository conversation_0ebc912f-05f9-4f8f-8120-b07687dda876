// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useRef } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    Platform,
} from "react-native";

import CompassIcon from "@components/compass_icon";
import { useTheme } from "@context/theme";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";

import type { SelectedEmoji } from "@components/post_draft/emoji_preview";

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 12,
            borderBottomColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 16,
            paddingVertical: 10,
            maxHeight: 100,
            minHeight: 100,
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 8,
        },
        title: {
            fontSize: 14,
            fontWeight: "600",
            color: theme.centerChannelColor,
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 6,
            borderRadius: 16,
        },
        doneButtonText: {
            color: theme.buttonColor,
            fontSize: 14,
            fontWeight: "600",
        },
        scrollContainer: {
            flex: 1,
        },
        scrollContent: {
            flexDirection: "row",
            alignItems: "center",
            paddingVertical: 4,
            paddingHorizontal: 16,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.6),
            fontSize: 12,
            fontStyle: "italic",
            textAlign: "center",
            flex: 1,
            paddingVertical: 8,
        },
        emojiItem: {
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: changeOpacity(theme.buttonBg, 0.12),
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginRight: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.buttonBg, 0.25),
            minWidth: 50,
            flexShrink: 0,
        },
        emojiCharacter: {
            fontSize: 16,
            marginRight: 4,
        },
        removeButton: {
            padding: 2,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
            minWidth: 20,
            minHeight: 20,
            alignItems: "center",
            justifyContent: "center",
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
        },
    });
});

const EmojiPickerPreview = ({
    selectedEmojis,
    onRemoveEmoji,
    onDone,
    testID = "emoji_picker_preview",
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const scrollViewRef = useRef<ScrollView>(null);

    const handleRemoveEmoji = useCallback(
        (id: string) => {
            onRemoveEmoji(id);
        },
        [onRemoveEmoji]
    );

    const handleDone = useCallback(() => {
        onDone();
    }, [onDone]);

    // Auto-scroll to the end when new emojis are selected
    useEffect(() => {
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollToEnd({ animated: true });
        }
    }, [selectedEmojis.length]);

    const scrollConfig = {
        ios: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "fast" as const,
            bounces: true,
            alwaysBounceHorizontal: true,
            alwaysBounceVertical: false,
        },
        android: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "normal" as const,
            bounces: false,
            alwaysBounceHorizontal: false,
            alwaysBounceVertical: false,
        },
    };

    const currentScrollConfig =
        Platform.OS === "ios" ? scrollConfig.ios : scrollConfig.android;



    if (selectedEmojis.length === 0) {
        return (
            <View style={styles.container} testID={`${testID}.container`}>
                <View style={styles.header}>
                    <Text style={styles.title}>Selected (0)</Text>
                </View>
                <Text style={styles.emptyText}>Tap emojis to select them</Text>
            </View>
        );
    }

    return (
        <View style={styles.container} testID={`${testID}.container`}>
            <View style={styles.header}>
                <Text style={styles.title}>
                    Selected ({selectedEmojis.length})
                </Text>
                <TouchableOpacity
                    style={styles.doneButton}
                    onPress={handleDone}
                    testID={`${testID}.done_button`}
                >
                    <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
            </View>
            <View style={styles.scrollContainer}>
                <ScrollView
                    ref={scrollViewRef}
                    horizontal
                    showsHorizontalScrollIndicator={true}
                    contentContainerStyle={styles.scrollContent}
                    testID={`${testID}.scroll_view`}
                    bounces={currentScrollConfig.bounces}
                    scrollEventThrottle={
                        currentScrollConfig.scrollEventThrottle
                    }
                    decelerationRate={currentScrollConfig.decelerationRate}
                    nestedScrollEnabled={
                        currentScrollConfig.nestedScrollEnabled
                    }
                    alwaysBounceHorizontal={
                        currentScrollConfig.alwaysBounceHorizontal
                    }
                    alwaysBounceVertical={
                        currentScrollConfig.alwaysBounceVertical
                    }
                    removeClippedSubviews={false}
                    keyboardShouldPersistTaps="handled"
                    directionalLockEnabled={true}
                    scrollEnabled={true}
                    pagingEnabled={false}
                    disableIntervalMomentum={false}

                >
                    {selectedEmojis.map((emoji) => (
                        <View
                            key={emoji.id}
                            style={styles.emojiItem}
                            testID={`${testID}.emoji_item.${emoji.id}`}
                        >
                            <Text style={styles.emojiCharacter}>
                                {emoji.character}
                            </Text>
                            <TouchableOpacity
                                style={styles.removeButton}
                                onPress={() => handleRemoveEmoji(emoji.id)}
                                testID={`${testID}.remove_button.${emoji.id}`}
                                hitSlop={{
                                    top: 8,
                                    bottom: 8,
                                    left: 8,
                                    right: 8,
                                }}
                                activeOpacity={0.7}
                            >
                                <CompassIcon
                                    name="close"
                                    size={10}
                                    style={styles.removeIcon}
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            </View>
        </View>
    );
};

export default EmojiPickerPreview;
