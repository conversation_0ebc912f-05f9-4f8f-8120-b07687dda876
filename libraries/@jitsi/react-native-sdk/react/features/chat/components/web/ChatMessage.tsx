import React from 'react';
import { connect } from 'react-redux';

import { type IReduxState } from '../../../app/types';
import { translate } from '../../../base/i18n/functions';
import MessageComponent from '../../../base/react/components/web/Message';
import { MESSAGE_TYPE_LOCAL } from '../../constants';
import { getCanReplyToMessage, getFormattedTimestamp, getMessageText, getPrivateNoticeMessage } from '../../functions';
import { type IChatMessageProps } from '../../types';

import PrivateMessageButton from './PrivateMessageButton';

interface IProps extends IChatMessageProps {

    type: string;
}

// Simple styles object without external dependencies
const styles = {
    chatMessageWrapper: {
        maxWidth: '100%'
    },

    chatMessage: {
        display: 'inline-flex',
        padding: '12px',
        backgroundColor: '#f5f5f5',
        borderRadius: '4px 12px 12px 12px',
        maxWidth: '100%',
        marginTop: '4px',
        boxSizing: 'border-box' as const
    },

    chatMessagePrivate: {
        backgroundColor: '#e8f4fd'
    },

    chatMessageLocal: {
        backgroundColor: '#e3f2fd',
        borderRadius: '12px 4px 12px 12px'
    },

    chatMessageError: {
        backgroundColor: '#ffebee',
        borderRadius: '0',
        fontWeight: 100
    },

    chatMessageLobby: {
        backgroundColor: '#e8f4fd'
    },

    replyWrapper: {
        display: 'flex',
        flexDirection: 'row' as const,
        alignItems: 'center',
        maxWidth: '100%'
    },

    messageContent: {
        maxWidth: '100%',
        overflow: 'hidden',
        flex: 1
    },

    replyButtonContainer: {
        display: 'flex',
        alignItems: 'flex-start',
        height: '100%'
    },

    replyButton: {
        padding: '2px'
    },

    displayName: {
        fontSize: '14px',
        fontWeight: 'bold',
        lineHeight: '20px',
        color: '#666',
        whiteSpace: 'nowrap' as const,
        textOverflow: 'ellipsis',
        overflow: 'hidden',
        marginBottom: '4px'
    },

    userMessage: {
        fontSize: '14px',
        lineHeight: '20px',
        color: '#333',
        whiteSpace: 'pre-wrap' as const,
        wordBreak: 'break-word' as const
    },

    privateMessageNotice: {
        fontSize: '12px',
        lineHeight: '16px',
        color: '#666',
        marginTop: '4px'
    },

    timestamp: {
        fontSize: '12px',
        lineHeight: '16px',
        color: '#999',
        marginTop: '4px'
    }
};

/**
 * Renders a single chat message.
 *
 * @param {IProps} props - Component's props.
 * @returns {JSX}
 */
const ChatMessage = ({
    canReply,
    knocking,
    message,
    showDisplayName,
    showTimestamp,
    type,
    t
}: IProps) => {
    // Helper function to combine class names
    const cx = (...classNames: (string | undefined | false)[]) =>
        classNames.filter(Boolean).join(' ');

    /**
     * Renders the display name of the sender.
     *
     * @returns {React$Element<*>}
     */
    function _renderDisplayName() {
        return (
            <div
                aria-hidden = { true }
                className = { cx('display-name') }
                style = { styles.displayName }>
                {message.displayName}
            </div>
        );
    }

    /**
     * Renders the message privacy notice.
     *
     * @returns {React$Element<*>}
     */
    function _renderPrivateNotice() {
        return (
            <div style = { styles.privateMessageNotice }>
                {getPrivateNoticeMessage(message)}
            </div>
        );
    }

    /**
     * Renders the time at which the message was sent.
     *
     * @returns {React$Element<*>}
     */
    function _renderTimestamp() {
        return (
            <div
                className = { cx('timestamp') }
                style = { styles.timestamp }>
                {getFormattedTimestamp(message)}
            </div>
        );
    }

    // Helper function to get chat message style based on type
    const getChatMessageStyle = () => {
        let style = { ...styles.chatMessage };

        if (message.privateMessage) {
            style = { ...style, ...styles.chatMessagePrivate };
        }

        if (type === 'local') {
            style = { ...style, ...styles.chatMessageLocal };
        }

        if (type === 'error') {
            style = { ...style, ...styles.chatMessageError };
        }

        if (message.lobbyChat && !knocking) {
            style = { ...style, ...styles.chatMessageLobby };
        }

        return style;
    };

    return (
        <div
            className = { cx(type) }
            style = { styles.chatMessageWrapper }
            id = { message.messageId }
            tabIndex = { -1 }>
            <div
                className = { cx('chatmessage', type,
                    message.privateMessage && 'privatemessage',
                    message.lobbyChat && !knocking && 'lobbymessage') }
                style = { getChatMessageStyle() }>
                <div style = { styles.replyWrapper }>
                    <div
                        className = { cx('messagecontent') }
                        style = { styles.messageContent }>
                        {showDisplayName && _renderDisplayName()}
                        <div
                            className = { cx('usermessage') }
                            style = { styles.userMessage }>
                            <span className = 'sr-only'>
                                {message.displayName === message.recipient
                                    ? String(t('chat.messageAccessibleTitleMe'))
                                    : String(t('chat.messageAccessibleTitle',
                                        { user: message.displayName }))}
                            </span>
                            {React.createElement(MessageComponent as any, { text: getMessageText(message) })}
                        </div>
                        {(message.privateMessage || (message.lobbyChat && !knocking))
                            && message.messageType === MESSAGE_TYPE_LOCAL
                            && _renderPrivateNotice()}
                    </div>
                    {canReply
                        && (
                            <div style = { styles.replyButtonContainer }>
                                <PrivateMessageButton
                                    isLobbyMessage = { message.lobbyChat }
                                    participantID = { message.participantId } />
                            </div>
                        )}
                </div>
            </div>
            {showTimestamp && _renderTimestamp()}
        </div>
    );
};

/**
 * Maps part of the Redux store to the props of this component.
 *
 * @param {Object} state - The Redux state.
 * @returns {IProps}
 */
function _mapStateToProps(state: IReduxState, { message }: IProps) {
    const { knocking } = state['features/lobby'];

    return {
        canReply: getCanReplyToMessage(state, message),
        knocking
    };
}

export default (translate as any)(connect(_mapStateToProps)(ChatMessage));
